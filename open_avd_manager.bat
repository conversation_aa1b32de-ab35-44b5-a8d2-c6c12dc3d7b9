@echo off
echo Opening AVD Manager to create/start emulator...

REM Try to open AVD Manager
echo Starting AVD Manager...
start avdmanager list avd

REM Alternative: Try to open Android Studio AVD Manager
echo If AVD Manager doesn't open, trying Android Studio...
start "" "C:\Program Files\Android\Android Studio\bin\studio64.exe"

echo.
echo Instructions:
echo 1. If AVD Manager opened, select an emulator and click Start
echo 2. If Android Studio opened, go to Tools > AVD Manager
echo 3. Create a new emulator if none exists
echo 4. Start the emulator
echo 5. Then run: flutter run

pause
