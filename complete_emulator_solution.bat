@echo off
chcp 65001 >nul
title Complete Flutter Emulator Solution

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🚀 Complete Emulator Solution             ║
echo ║                      حل شامل لفتح المحاكي                    ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo Step 1: Opening Android Studio to manage emulators...
echo خطوة 1: فتح Android Studio لإدارة المحاكيات...

REM Try different paths for Android Studio
if exist "C:\Program Files\Android\Android Studio\bin\studio64.exe" (
    echo Found Android Studio, opening...
    start "" "C:\Program Files\Android\Android Studio\bin\studio64.exe"
) else if exist "%LOCALAPPDATA%\Android\Sdk\tools\bin\avdmanager.bat" (
    echo Found AVD Manager, listing devices...
    "%LOCALAPPDATA%\Android\Sdk\tools\bin\avdmanager.bat" list avd
) else if exist "%ANDROID_HOME%\tools\bin\avdmanager.bat" (
    echo Using ANDROID_HOME path...
    "%ANDROID_HOME%\tools\bin\avdmanager.bat" list avd
) else (
    echo Android Studio not found in common locations.
    echo Please manually open Android Studio and go to Tools > AVD Manager
)

echo.
echo Step 2: Instructions for creating/starting emulator:
echo خطوة 2: تعليمات إنشاء/تشغيل المحاكي:
echo.
echo 1. In Android Studio, go to: Tools > AVD Manager
echo 2. If no emulator exists, click "Create Virtual Device"
echo 3. Choose a device (e.g., Pixel 4) and click Next
echo 4. Choose a system image (e.g., API 30) and click Next
echo 5. Click Finish to create the emulator
echo 6. Click the Play button to start the emulator
echo.
echo Step 3: Once emulator is running, press any key to start Flutter app...
echo خطوة 3: عندما يعمل المحاكي، اضغط أي مفتاح لتشغيل التطبيق...
pause

echo.
echo Step 4: Starting Flutter app...
echo خطوة 4: تشغيل تطبيق Flutter...
echo.

echo Cleaning project...
flutter clean

echo Getting dependencies...
flutter pub get

echo Running app on emulator...
flutter run

echo.
echo ✅ Done! / تم الانتهاء!
pause
