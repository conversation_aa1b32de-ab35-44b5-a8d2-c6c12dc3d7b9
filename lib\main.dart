import 'package:flutter/material.dart';

// Global notification instance (معطل مؤقتاً)
// final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
//     FlutterLocalNotificationsPlugin();

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // تهيئة جميع الخدمات
  await _initializeServices();

  runApp(const ProviderScope(child: MyApp()));
}

/// تهيئة جميع خدمات التطبيق
Future<void> _initializeServices() async {
  try {
    // تهيئة التخزين المحلي
    await StorageService.initialize();

    // تهيئة الإشعارات
    await NotificationService.initialize();
    await AdvancedNotificationService.initialize();

    // تهيئة خدمة المصادقة
    await AuthService.initialize();

    // تهيئة خدمة التحليلات
    await UsageAnalytics.initialize();

    // تهيئة خدمة العمل بدون إنترنت
    await OfflineService.initialize();

    // تهيئة خدمة الأمان
    await SecurityService.initialize();

    // تهيئة خدمة الأداء
    await PerformanceService.initialize();

    // تهيئة خدمة الصوت
    await VoiceService.initialize();

    // تهيئة خدمة الدفع
    await PaymentService.initialize();

    // تهيئة الخدمات السحابية
    await CloudStorageService.initialize();
    await CloudDatabaseService.initialize();
    await PushNotificationService.initialize();

    // تهيئة خدمات التعاون
    await UserManagementService.initialize();
    await WorkspaceService.initialize();
    await TeamChatService.initialize();

    // تهيئة خدمات التحليلات
    await AIAnalyticsEngine.initialize();
    await DashboardService.initialize();

    // تهيئة خدمات المنصات
    await PlatformSupportService.initialize();
    await CompanionAppService.initialize();

    // تهيئة خدمات الأداء
    await PerformanceOptimizer.initialize();
    await MemoryManager.initialize();

    // تهيئة خدمات الاختبار والتوثيق
    await TestSuiteManager.initialize();
    await DocumentationGenerator.initialize();

    // تهيئة خدمة إدارة API
    await ApiProviderService.initialize();

    // تهيئة خدمة الذكاء الاصطناعي
    await EnhancedAIService.initialize();

    // تهيئة نظام الأدوات الحقيقية
    await RealAIToolsHub.initialize();

    // تنظيف قاعدة البيانات
    await StorageService.clearExpiredCache();

    debugPrint('✅ تم تهيئة جميع الخدمات بنجاح');
  } catch (e) {
    debugPrint('❌ خطأ في تهيئة الخدمات: $e');
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      title: 'DeepSeek AI',
      theme: ThemeData(
        // fontFamily: 'Urbanist',
        scaffoldBackgroundColor: AppColors.background,
        colorScheme: ColorScheme(
          brightness: Brightness.dark,
          primary: AppColors.primaryPurple,
          onPrimary: AppColors.white,
          secondary: AppColors.lightPurple,
          onSecondary: AppColors.white,
          error: AppColors.error,
          onError: AppColors.white,
          surface: AppColors.darkGrey,
          onSurface: AppColors.white,
        ),
        useMaterial3: true,
        appBarTheme: const AppBarTheme(
          backgroundColor: AppColors.darkPurple,
          elevation: 0,
          iconTheme: IconThemeData(color: AppColors.white),
          titleTextStyle: TextStyle(
            // fontFamily: 'Urbanist',
            fontWeight: FontWeight.bold,
            fontSize: 20,
            color: AppColors.white,
          ),
        ),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primaryPurple,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(24),
            ),
            textStyle: const TextStyle(
              // fontFamily: 'Urbanist',
              fontWeight: FontWeight.w600,
              fontSize: 16,
            ),
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 32),
            elevation: 4,
            shadowColor: AppColors.primaryPurple,
          ),
        ),
        outlinedButtonTheme: OutlinedButtonThemeData(
          style: OutlinedButton.styleFrom(
            side: const BorderSide(color: AppColors.primaryPurple),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(24),
            ),
            textStyle: const TextStyle(
              // fontFamily: 'Urbanist',
              fontWeight: FontWeight.w600,
              fontSize: 16,
            ),
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 32),
          ),
        ),
        inputDecorationTheme: InputDecorationTheme(
          filled: true,
          fillColor: AppColors.darkGrey,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide.none,
          ),
          hintStyle: const TextStyle(
            color: Colors.white54,
            // fontFamily: 'Urbanist',
          ),
          labelStyle: const TextStyle(
            color: AppColors.lightPurple,
            // fontFamily: 'Urbanist',
          ),
        ),
        textTheme: const TextTheme(
          displayLarge: TextStyle(
            // fontFamily: 'Urbanist',
            fontWeight: FontWeight.bold,
            fontSize: 32,
            color: AppColors.white,
          ),
          displayMedium: TextStyle(
            // fontFamily: 'Urbanist',
            fontWeight: FontWeight.bold,
            fontSize: 28,
            color: AppColors.white,
          ),
          displaySmall: TextStyle(
            // fontFamily: 'Urbanist',
            fontWeight: FontWeight.w600,
            fontSize: 22,
            color: AppColors.white,
          ),
          titleLarge: TextStyle(
            // fontFamily: 'Urbanist',
            fontWeight: FontWeight.w600,
            fontSize: 20,
            color: AppColors.white,
          ),
          bodyLarge: TextStyle(
            // fontFamily: 'Urbanist',
            fontWeight: FontWeight.normal,
            fontSize: 16,
            color: AppColors.white,
          ),
          bodyMedium: TextStyle(
            // fontFamily: 'Urbanist',
            fontWeight: FontWeight.normal,
            fontSize: 14,
            color: Colors.white70,
          ),
          labelLarge: TextStyle(
            // fontFamily: 'Urbanist',
            fontWeight: FontWeight.w600,
            fontSize: 16,
            color: AppColors.white,
          ),
        ),
      ),
      initialRoute: '/',
      routes: {
        '/': (context) => const OnboardingScreen(),
        '/auth': (context) => const AuthScreen(),
        '/home': (context) => const EnhancedHomeScreen(),
        '/chat': (context) => const ChatScreen(),
        '/enhanced_chat': (context) => const EnhancedChatScreen(),
        '/enhanced_settings': (context) => const EnhancedSettingsScreen(),
        '/side_menu': (context) => const SideMenu(),
        '/create_image': (context) => const CreateImageScreen(),
        '/summarize_text': (context) => const SummarizeTextScreen(),
        '/analyze_data': (context) => const AnalyzeDataScreen(),
        '/plan': (context) => const PlanScreen(),
        '/write_assist': (context) => const WriteAssistScreen(),
        '/browse': (context) => const BrowseScreen(),
        '/unified_api_management':
            (context) => const UnifiedApiManagementScreen(),
        '/system_status': (context) => const SystemStatusScreen(),
        '/image_analysis': (context) => const ImageAnalysisScreen(),
        '/smart_translation': (context) => const SmartTranslationScreen(),
        '/analytics': (context) => const AnalyticsScreen(),
        '/voice_chat': (context) => const VoiceChatScreen(),
        '/subscription': (context) => const SubscriptionScreen(),
        '/modern_home': (context) => const ModernHomeScreen(),
        '/cloud_management': (context) => const CloudManagementScreen(),
        '/collaboration': (context) => const CollaborationScreen(),
        '/ai_analytics': (context) => const AIAnalyticsScreen(),
        '/platform_management': (context) => const PlatformManagementScreen(),
        '/performance_monitor': (context) => const PerformanceMonitorScreen(),
        '/testing_documentation': (context) => const TestingDocumentationScreen(),
      },
    );
  }
}
