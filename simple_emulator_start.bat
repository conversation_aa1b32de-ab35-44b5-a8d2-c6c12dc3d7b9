@echo off
echo Starting Android Studio for emulator management...

if exist "C:\Program Files\Android\Android Studio\bin\studio64.exe" (
    echo Opening Android Studio...
    start "" "C:\Program Files\Android\Android Studio\bin\studio64.exe"
    echo Android Studio is opening...
    echo Please go to Tools > AVD Manager to start an emulator
) else (
    echo Android Studio not found at default location
    echo Please open Android Studio manually and go to Tools > AVD Manager
)

echo.
echo Instructions:
echo 1. Open Android Studio
echo 2. Go to Tools > AVD Manager  
echo 3. Start an existing emulator OR create a new one
echo 4. Wait for emulator to fully boot
echo 5. Press any key here to run Flutter app

pause

echo.
echo Running Flutter app...
flutter clean
flutter pub get
flutter run

pause
