@echo off
echo Trying to start emulator directly...

REM Common emulator names to try
echo Attempting to start common emulator configurations...

emulator @Pixel_4_API_30 >nul 2>&1 &
if not errorlevel 1 (
    echo Pixel_4_API_30 emulator starting...
    goto :wait_and_run
)

emulator @Pixel_3a_API_30 >nul 2>&1 &
if not errorlevel 1 (
    echo Pixel_3a_API_30 emulator starting...
    goto :wait_and_run
)

emulator @Pixel_5_API_30 >nul 2>&1 &
if not errorlevel 1 (
    echo Pixel_5_API_30 emulator starting...
    goto :wait_and_run
)

emulator @Medium_Phone_API_30 >nul 2>&1 &
if not errorlevel 1 (
    echo Medium_Phone_API_30 emulator starting...
    goto :wait_and_run
)

echo No common emulator found. Please use Android Studio to create one.
echo Run simple_emulator_start.bat instead.
pause
exit

:wait_and_run
echo Waiting for emulator to boot completely...
echo This may take 2-3 minutes...
timeout /t 120 /nobreak

echo Running Flutter app...
flutter clean
flutter pub get
flutter run

pause
