@echo off
echo Opening Android Emulator...

REM Try to start a common emulator name
echo Trying to start emulator...
start /B emulator @Pixel_3a_API_30_x86

REM Wait a bit for emulator to start
timeout /t 10 /nobreak

REM Alternative emulator names to try
if errorlevel 1 (
    echo Trying alternative emulator names...
    start /B emulator @Pixel_4_API_30
)

if errorlevel 1 (
    start /B emulator @Pixel_5_API_30
)

if errorlevel 1 (
    start /B emulator @Medium_Phone_API_30
)

echo Emulator starting... Please wait for it to fully boot.
echo Then run: flutter run

pause
