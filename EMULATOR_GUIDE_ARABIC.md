# دليل تشغيل المحاكي وتطبيق Flutter

## الطرق المتاحة لتشغيل التطبيق:

### الطريقة 1: استخدام Android Studio (الأسهل)
```
.\simple_emulator_start.bat
```
- سيفتح Android Studio
- اذهب إلى Tools > AVD Manager
- اختر محاكي موجود أو أنشئ واحد جديد
- اضغط زر التشغيل للمحاكي
- انتظر حتى يكتمل تحميل المحاكي
- ارجع للـ terminal واضغط أي مفتاح

### الطريقة 2: تشغيل مباشر للمحاكي
```
.\direct_emulator_start.bat
```
- سيحاول فتح محاكي بأسماء شائعة
- إذا نجح، انتظر 2-3 دقائق
- سيشغل التطبيق تلقائياً

### الطريقة 3: يدوياً
1. افتح Android Studio
2. اذه<PERSON> إلى Tools > AVD Manager
3. شغ<PERSON> محاكي
4. في terminal اكتب:
```
flutter clean
flutter pub get
flutter run
```

## إنشاء محاكي جديد (إذا لم يكن موجود):
1. في Android Studio > Tools > AVD Manager
2. اضغط "Create Virtual Device"
3. اختر Pixel 4 أو أي جهاز
4. اختر API Level 30 أو أحدث
5. اضغط Finish
6. شغل المحاكي

## استكشاف الأخطاء:
- إذا لم يعمل المحاكي: تأكد من تثبيت Android SDK
- إذا كان Flutter بطيء: انتظر - المرة الأولى تأخذ وقت
- إذا ظهرت أخطاء: شغل `flutter doctor` للتحقق من الإعدادات

## الملفات المتاحة:
- `simple_emulator_start.bat` - الطريقة الأسهل
- `direct_emulator_start.bat` - تشغيل مباشر
- `simple_run.bat` - تشغيل التطبيق فقط (بعد فتح المحاكي)
